import json
import os
from pathlib import Path
import requests
from celery import chain, shared_task
from decouple import config
from django.core.files.base import ContentFile
from django.db import transaction, DatabaseError
from django.db.transaction import TransactionManagementError
from cls_backend.constants import TASK_TIMEOUT, OCR_TIMEOUT
from django.db.models import Count
from django.core.serializers.json import DjangoJSONEncoder
from cls_backend import constants as const
from cls_backend.models import Document
from cls_backend.modules.notification.event import EVENT_CDVB_DONE
from cls_backend.modules.notification.utils import send_notifications
from cls_backend.modules.ocr.serializers import DocumentSerializer
from cls_backend.tasks.saved_converted_file import save_converted_file
from cls_backend.utils.cal_timeout_acord_page import get_timeout_limit_from_document_id
from cls_backend.utils.file_utils import generate_presigned_url
from cls_backend.utils.timeout_terminate_celery import BaseTask
from .handle_ie import handle_ie
from cls_backend.tasks.handle_extract_json import handle_extract_json
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from django.core.files.base import ContentFile
from cls_backend.utils.handle_file import RedisUtil
import logging
from contextlib import contextmanager
import time
from .handle_conflict import handle_conflict
import subprocess
import tempfile

logger = logging.getLogger("cls")


def create_requests_session():
    """Create a requests session with retry strategy"""
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[408, 429, 500, 502, 503, 504]
    )
    session.mount('http://', HTTPAdapter(max_retries=retries))
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

@contextmanager
def timeout_transaction(bind=True, base=BaseTask,timeout_seconds=TASK_TIMEOUT):  # 5 minutes default timeout
    start_time = time.time()
    try:
        with transaction.atomic():
            yield
            if time.time() - start_time > timeout_seconds:
                raise TimeoutError("Transaction timeout exceeded")
    except (DatabaseError, TimeoutError, TransactionManagementError) as e:
        logger.exception(e)
        transaction.set_rollback(True)
        raise

@shared_task(bind=True, max_retries=2)
def handle_ocr(self, document_id, skip_notif=False, skip_process=False):
    try:
        
        logger.debug(f"Start handling OCR document id {document_id}")
        document = Document.objects.get(pk=document_id)
        soft_timeout, hard_timeout = get_timeout_limit_from_document_id(document.id)
        # Skip if document is already processed
        if document.is_convert and document.convert and not skip_process:
            handle_ie.apply_async(
                kwargs={'document_id': document.id},
                queue='ocr',
                soft_time_limit=soft_timeout,
                time_limit=hard_timeout
            )
            logger.debug('Document is converted')
            handle_conflict.delay(document_id=document_id)
            return

        user_id = document.created_by.id
        # document.status = const.STATUS_PROCESSING
        document.status = const.STATUS_OCR
        document.save(update_fields=['status'])
        
        extension = document.name.lower().split('.')[-1]
        # Nếu là file .doc thì chuyển sang docx trước
        if extension == 'doc':
            tmp_dir = tempfile.gettempdir()
            local_doc_path = os.path.join(tmp_dir, document.origin.name.split('/')[-1])
            with document.origin.open('rb') as f:
                with open(local_doc_path, 'wb') as out_f:
                    out_f.write(f.read())
            # Chuyển đổi sang docx
            docx_path = convert_doc_to_docx(local_doc_path, tmp_dir)
            if not docx_path or not os.path.exists(docx_path):
                logger.error("Failed to convert .doc to .docx")
                document.status = const.STATUS_FAILED
                document.save(update_fields=['status'])
                return
            # Upload lại file docx lên S3, cập nhật document.origin
            with open(docx_path, 'rb') as docx_file:
                document.origin.save(os.path.basename(docx_path), ContentFile(docx_file.read()), save=True)
            # Xóa file tạm
            os.remove(local_doc_path)
            os.remove(docx_path)
            # Cập nhật lại extension để tiếp tục xử lý như docx
            extension = 'docx'
        if extension == 'docx':
            logger.debug('Document is docx/doc => Run into ocr extract directly')
            handle_extract_json.delay(document.id)
            logger.debug('Finished ocr extract')
            if skip_process:
                document.status = const.STATUS_SUCCESS
                document.save(update_fields=['status'])
            else:
                handle_conflict.delay(document_id=document_id)
                handle_ie.apply_async(
                    kwargs={'document_id': document.id},
                    queue='ocr',
                    soft_time_limit=soft_timeout,
                    time_limit=hard_timeout
                )
            return

        # Second transaction with timeout
        with timeout_transaction(soft_timeout):
            logger.debug("processing")
            # document = Document.objects.select_for_update().get(pk=document_id)
            # if document.is_convert and not document.convert:
            try:
                if not document.origin or not document.origin.storage.exists(document.origin.name):
                    logger.warning(f"Document {document_id} does not have a valid origin file. Skipping OCR.")
                    document.status = const.STATUS_SUCCESS
                    document.save(update_fields=['status'])
                    return
                # with document.origin.open('rb') as file:
                #     file_content = file.read()
                
                # files = {'file': ('document.pdf', file_content)}
                # payload = {'return_type': 'word', 'is_tile': 0, 'is_full_line': 1}
                s3_url = generate_presigned_url(document.origin)
                
                if not s3_url:
                    logger.error(f"Could not generate S3 URL for document {document_id}")
                    document.status = const.STATUS_FAILED
                    document.save(update_fields=['status'])
                    return
                s3_path = f"s3://{config('AWS_STORAGE_BUCKET_NAME')}/{document.origin.name}"
                # Gửi URL S3 thay vì file
                payload = {
                    'return_type': 'word', 
                    'langs': 'vn',
                    'is_tile': 0, 
                    'is_full_line': 1,
                    'is_extract_figure': 0,
                    's3_file': s3_path  # Thêm URL S3 vào payload
                }
                upload_url = config('OCR_HOST') + config('EXTRACT_OCR')
                get_data_url = config('OCR_HOST') + config('OCR_CHECK_STATUS')
                session = create_requests_session()
                
                logger.debug(f"Processing OCR for document {document_id} at {upload_url}, {payload}")
                response = session.post(
                    upload_url,
                    data=payload,
                    timeout=(1, 10), # num retries x timeout
                    stream=True
                )
                # logger.debug([upload_url, payload])
               
                logger.debug(response.text)
                if response.status_code == 200 and response.json()['status'] == "Success":
                    job_id = response.json()['session_id']
                    is_converted = RedisUtil.check_job_status_with_timeout(job_id, 'ocr', soft_timeout, check_interval = 3)
                    if is_converted:
                        # Gửi request để lấy thông tin về file đã xử lý
                        get_result_payload = {'session_id': job_id, 'return_type': 'word'}
                        response = requests.post(get_data_url, data=get_result_payload)
                        # response = RedisUtil.check_job_status_with_timeout(job_id, type_redis="word", time_limit=300)
                        # Kiểm tra response có thành công không
                        if response.status_code == 200:
                            try:
                                result_data = response.json()
                                # Kiểm tra status trong response
                                if result_data.get('status') == "Success":
                                    # Lấy thông tin file từ S3
                                    origin_name = Document.objects.get(id=document_id).origin.name
                                    base, _ = os.path.splitext(origin_name)
                                    docx_path = f"{base}.docx"
                                    # origin_name = Document.objects.get(id=document_id).origin.name
                                    # docx_path = Path(origin_name).with_suffix(".docx").name
                                    # Tạo URL S3 presigned để tải file
                                    docx_url = generate_presigned_url(docx_path)
                                    logger.debug(f"Presigned URL: {docx_url}")
                                    if docx_url:
                                        # Tải file từ S3
                                        s3_response = requests.get(docx_url)
                                        
                                        if s3_response.status_code == 200 and len(s3_response.content) > 0:
                                            # Lưu file vào trường convert                                          
                                            # document.convert.save(docx_path, ContentFile(s3_response.content), save=True)
                                            
                                            # # Cập nhật trạng thái
                                            # document.is_convert = True
                                            # document.status = const.STATUS_SUCCESS
                                            # document.save(update_fields=['is_convert', 'status'])
                                
                                            if not skip_process:
                                                # handle_conflict.delay(document_id=document_id)
                                                chain(
                                                    save_converted_file.s(document.id, docx_path, docx_url).set(queue='ocr'),
                                                    handle_extract_json.s().set(queue='default'),  # Là task dạng Celery, KHÔNG gọi trực tiếp
                                                    handle_conflict.s().set(queue='default')
                                                ).apply_async()
                                            else:
                                                chain(
                                                    save_converted_file.s(document.id, docx_path, docx_url).set(queue='ocr'),
                                                    handle_extract_json.s().set(queue='default')  # Là task dạng Celery, KHÔNG gọi trực tiếp
                                                ).apply_async()
                                            logger.debug(f"OCR convert successfully for document {document_id}, saved as {document.convert.name}")
                                        else:
                                            logger.error(f"Failed to download file from S3: {s3_response.status_code}")
                                            document.status = const.STATUS_FAILED
                                            document.save(update_fields=['status'])
                                            raise requests.RequestException(f"Failed to download file from S3: {s3_response.status_code}")
                                    else:
                                        logger.error(f"Failed to generate presigned URL for {docx_path}")
                                        document.status = const.STATUS_FAILED
                                        document.save(update_fields=['status'])
                                        raise ValueError(f"Failed to generate presigned URL for {docx_path}")
                                else:
                                    logger.error(f"OCR API returned non-success status: {result_data.get('status')}")
                                    document.status = const.STATUS_FAILED
                                    document.save(update_fields=['status'])
                                    raise ValueError(f"OCR API returned non-success status: {result_data.get('status')}")
                            except ValueError as e:
                                logger.error(f"Invalid JSON response from OCR API: {str(e)}")
                                document.status = const.STATUS_FAILED
                                document.save(update_fields=['status'])
                                raise ValueError(f"Invalid JSON response from OCR API: {str(e)}")
                        else:
                            logger.error(f"Failed to get result from OCR API: {response.status_code} - {response.text}")
                            document.status = const.STATUS_FAILED
                            document.save(update_fields=['status'])
                            raise requests.RequestException(f"Failed to get result from OCR API: {response.status_code}")

                else:
                    raise requests.RequestException(f"API returned status code: {response.status_code}")

            except (requests.RequestException, IOError) as e:
                logger.debug(f"Error in OCR API call for document {document_id}: {str(e)}")
                
                if self.request.retries < self.max_retries:
                    raise self.retry(countdown=30)
                
                document.status = const.STATUS_FAILED
                document.save(update_fields=['status'])
                raise e

            # handle_extract_json(document=document)

    except Exception as e:
        logger.exception(e)
        # document = Document.objects.get(pk=document_id)
        document.status = const.STATUS_FAILED
        document.save(update_fields=['status'])
        raise e
    
    if skip_process:    
        document.status = const.STATUS_SUCCESS
        document.save(update_fields=['status'])
        
    # Send notif in case of failure
    if not skip_notif:
        document.percentage = 25
        document.save(update_fields=['percentage'])
        document_serializer = DocumentSerializer(document)
        # send_notifications(user_id=user_id, event=EVENT_CDVB_DONE, data=document_serializer.data)
        data = json.loads(json.dumps(document_serializer.data, cls=DjangoJSONEncoder))
        send_notifications(user_id=user_id, event=EVENT_CDVB_DONE, data=data, percentage=25)

    if not skip_process:
        handle_ie.apply_async(
            kwargs={'document_id': document.id},
            queue='ocr',
            soft_time_limit=soft_timeout,
            time_limit=hard_timeout
        )

def convert_doc_to_docx(input_path, output_dir):
    """
    Convert a .doc file to .docx using LibreOffice.
    Returns the path to the converted .docx file.
    """
    try:
        # soffice_path = r"C:\Program Files\LibreOffice\program\soffice.exe"
        subprocess.run([
            # soffice_path, 
            "soffice",
            "--headless", "--convert-to", "docx", "--outdir", output_dir, input_path
        ], check=True)
        base = os.path.splitext(os.path.basename(input_path))[0]
        return os.path.join(output_dir, base + ".docx")
    except subprocess.CalledProcessError as e:
        logger.error(f"LibreOffice conversion failed: {e}")
        return None

    
