from celery import shared_task
from django.core.files.base import ContentFile
import requests
import logging

from cls_backend.models import Document
from cls_backend import constants as const

logger = logging.getLogger(__name__)

@shared_task(bind=True, max_retries=2)
def save_converted_file(self, document_id, docx_path, docx_url):
    try:
        logger.debug(f"Downloading converted file from {docx_url}")
        response = requests.get(docx_url)

        if response.status_code != 200 or len(response.content) == 0:
            raise Exception(f"Failed to download file: {response.status_code}")
        
        document = Document.objects.get(pk=document_id)
        document.convert.save(docx_path, ContentFile(response.content), save=True)
        document.is_convert = True
        if not document.convert or not document.convert.name or not document.convert.storage.exists(document.convert.name):
            raise ValueError(f"Convert file was not saved correctly for document {document_id}")
        # document.status = const.STATUS_SUCCESS
        # document.save(update_fields=['is_convert', 'status'])
        document.save(update_fields=['is_convert'])
        logger.info(f"Saved converted file for document {document_id}")

        # ✅ return document_id cho task sau
        return document_id

    except Exception as e:
        logger.error(f"Failed to save converted file for document {document_id}: {str(e)}")
        document = Document.objects.get(pk=document_id)
        document.status = const.STATUS_FAILED
        document.save(update_fields=['status'])
        raise self.retry(countdown=30, exc=e)

